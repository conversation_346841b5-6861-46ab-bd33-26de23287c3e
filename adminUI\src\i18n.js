import Vue from 'vue'
import VueI18n from 'vue-i18n'
import zhCN from './lang/zh-CN'
import en from './lang/en'
import id from './lang/id'

// 引入 Element UI 语言包
import elementZhCN from 'element-ui/lib/locale/lang/zh-CN'
import elementEn from 'element-ui/lib/locale/lang/en'
import elementId from 'element-ui/lib/locale/lang/id'

Vue.use(VueI18n)

const messages = {
  'zh-CN': {
    ...zhCN,
    ...elementZhCN
  },
  en: {
    ...en,
    ...elementEn
  },
  id: {
    ...id,
    ...elementId
  }
}

const i18n = new VueI18n({
 locale: localStorage.getItem('locale') || 'zh-CN',
  fallbackLocale: 'zh-CN',
  messages
})

export default i18n
